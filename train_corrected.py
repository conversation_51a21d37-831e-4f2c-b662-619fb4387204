#!/usr/bin/env python3
import torch
from transformers import Auto<PERSON>oken<PERSON>, AutoModelForCausalLM, TrainingArguments, Trainer, DataCollatorForLanguageModeling
from peft import LoraConfig, get_peft_model, TaskType
from torch.utils.data import Dataset

class SimpleDataset(Dataset):
    def __init__(self, data, tokenizer, max_length=512):
        self.data = data
        self.tokenizer = tokenizer
        self.max_length = max_length
        
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        text = f"Instruction: {item['instruction']}\nOutput: {item['output']}"
        
        encoding = self.tokenizer(
            text,
            truncation=True,
            max_length=self.max_length,
            padding="max_length",
            return_tensors="pt"
        )
        
        return {
            "input_ids": encoding["input_ids"].flatten(),
            "attention_mask": encoding["attention_mask"].flatten(),
            "labels": encoding["input_ids"].flatten()
        }

print("🚀 Loading local Qwen3-8B model...")

# Load from local directory
model_path = "./qwen3-8b-model"
tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True, local_files_only=True)
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

model = AutoModelForCausalLM.from_pretrained(
    model_path,
    torch_dtype=torch.float16,
    device_map="auto",
    trust_remote_code=True,
    local_files_only=True
)

print("✅ Model loaded successfully!")

# Setup LoRA
lora_config = LoraConfig(
    task_type=TaskType.CAUSAL_LM,
    r=16,
    lora_alpha=32,
    lora_dropout=0.1,
    target_modules=["q_proj", "k_proj", "v_proj", "o_proj"]
)

model = get_peft_model(model, lora_config)
print(f"📊 Trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

# Create Python coding data
print("📚 Creating Python coding dataset...")
data = [
    {"instruction": "Write a function to add two numbers", "output": "def add(a, b):\n    return a + b"},
    {"instruction": "Create a function to multiply two numbers", "output": "def multiply(a, b):\n    return a * b"},
    {"instruction": "Write a function to find the maximum of two numbers", "output": "def max_num(a, b):\n    return max(a, b)"},
    {"instruction": "Create a function to check if a number is even", "output": "def is_even(n):\n    return n % 2 == 0"},
    {"instruction": "Write a function to reverse a string", "output": "def reverse_string(s):\n    return s[::-1]"},
    {"instruction": "Create a function to calculate factorial", "output": "def factorial(n):\n    if n <= 1:\n        return 1\n    return n * factorial(n-1)"},
    {"instruction": "Write a function to check if a string is palindrome", "output": "def is_palindrome(s):\n    return s == s[::-1]"},
    {"instruction": "Create a function to find length of a list", "output": "def list_length(lst):\n    return len(lst)"},
]

print(f"📝 Using {len(data)} examples for training")

# Create dataset
dataset = SimpleDataset(data, tokenizer)

# Training arguments - FIXED for FP16 issues
training_args = TrainingArguments(
    output_dir="./qwen3-finetuned",
    num_train_epochs=1,
    per_device_train_batch_size=1,
    gradient_accumulation_steps=2,
    learning_rate=2e-4,
    # Use BF16 instead of FP16 - more stable
    bf16=torch.cuda.is_bf16_supported(),  # Auto-detect BF16 support
    fp16=False,  # Disable FP16 completely
    logging_steps=2,
    save_steps=10,
    remove_unused_columns=False,
    dataloader_pin_memory=False,
    gradient_checkpointing=True,
    max_grad_norm=1.0,
    # Disable automatic mixed precision scaling
    dataloader_num_workers=0,
)

# Data collator
data_collator = DataCollatorForLanguageModeling(
    tokenizer=tokenizer,
    mlm=False,
)

# Create trainer
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=dataset,
    data_collator=data_collator,
)

print("🎯 Starting training...")
trainer.train()

print("✅ Training completed!")
trainer.save_model("./qwen3-finetuned-final")
print("💾 Model saved to ./qwen3-finetuned-final")

print("🎉 Fine-tuning completed successfully!")
