#!/usr/bin/env python3
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import LoraConfig, get_peft_model, TaskType
import requests
import json

print("🚀 Loading local Qwen3-8B model...")

# Load from local directory
model_path = "./qwen3-8b-model"
tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True, local_files_only=True)
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

model = AutoModelForCausalLM.from_pretrained(
    model_path,
    torch_dtype=torch.float16,
    device_map="auto",
    trust_remote_code=True,
    local_files_only=True
)

print("✅ Model loaded successfully!")

# Setup LoRA
lora_config = LoraConfig(
    task_type=TaskType.CAUSAL_LM,
    r=16,
    lora_alpha=32,
    lora_dropout=0.1,
    target_modules=["q_proj", "k_proj", "v_proj", "o_proj"]
)

model = get_peft_model(model, lora_config)
print(f"📊 Trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

# Download dataset directly
print("📚 Downloading dataset...")
url = "https://huggingface.co/datasets/iamtarun/python_code_instructions_18k_alpaca/resolve/main/python_code_instructions_18k_alpaca.json"
response = requests.get(url)
data = response.json()[:100]  # Just 100 examples for quick test

print(f"✅ Loaded {len(data)} examples")
print("🎯 Ready for training!")
print("Model and data loaded successfully. Training setup complete!")
