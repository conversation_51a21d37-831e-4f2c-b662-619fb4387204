#!/usr/bin/env python3
"""
Fine-tune Qwen3-8B on Python code instructions dataset
Using proper Qwen3 model with agentic capabilities
"""

import os
import torch
from datasets import load_dataset
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling
)
from peft import LoraConfig, get_peft_model, TaskType
import json

def setup_environment():
    print("🚀 Setting up Qwen3 fine-tuning environment...")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name(0)}")
        print(f"VRAM: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")

def load_model_and_tokenizer():
    print("📦 Loading Qwen3-8B model and tokenizer...")
    
    # Using the actual Qwen3-8B model
    model_name = "Qwen/Qwen3-8B"
    
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        torch_dtype="auto",
        device_map="auto",
        trust_remote_code=True
    )
    
    return model, tokenizer

def setup_lora_config():
    print("⚙️ Setting up LoRA configuration for Qwen3...")
    
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        r=16,
        lora_alpha=32,
        lora_dropout=0.1,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
        bias="none",
    )
    
    return lora_config

def prepare_dataset(tokenizer):
    print("📚 Loading dataset: iamtarun/python_code_instructions_18k_alpaca...")
    
    dataset = load_dataset("iamtarun/python_code_instructions_18k_alpaca")
    
    def format_instruction(example):
        instruction = example.get('instruction', '')
        input_text = example.get('input', '')
        output = example.get('output', '')
        
        # Format as chat messages for Qwen3
        messages = [
            {"role": "user", "content": f"{instruction}\n\n{input_text}" if input_text else instruction}
        ]
        
        # Apply chat template with thinking disabled for training efficiency
        prompt = tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True,
            enable_thinking=False  # Disable thinking mode for training
        )
        
        # Add the response
        full_text = prompt + output + tokenizer.eos_token
        
        return {"text": full_text}
    
    formatted_dataset = dataset.map(format_instruction)
    
    def tokenize_function(examples):
        tokenized = tokenizer(
            examples["text"],
            truncation=True,
            padding=False,
            max_length=2048,
            return_tensors=None
        )
        tokenized["labels"] = tokenized["input_ids"].copy()
        return tokenized
    
    tokenized_dataset = formatted_dataset.map(
        tokenize_function,
        batched=True,
        remove_columns=formatted_dataset["train"].column_names
    )
    
    return tokenized_dataset

def main():
    setup_environment()
    
    model, tokenizer = load_model_and_tokenizer()
    
    lora_config = setup_lora_config()
    model = get_peft_model(model, lora_config)
    
    print(f"📊 Trainable parameters: {model.num_parameters():,}")
    
    dataset = prepare_dataset(tokenizer)
    train_dataset = dataset["train"].train_test_split(test_size=0.1, seed=42)
    
    print(f"📊 Training samples: {len(train_dataset['train']):,}")
    print(f"📊 Validation samples: {len(train_dataset['test']):,}")
    
    training_args = TrainingArguments(
        output_dir="./qwen3-python-finetune",
        num_train_epochs=2,
        per_device_train_batch_size=1,
        gradient_accumulation_steps=16,
        warmup_steps=50,
        learning_rate=2e-4,
        fp16=False,
        bf16=True,
        logging_steps=10,
        evaluation_strategy="steps",
        eval_steps=100,
        save_steps=500,
        save_total_limit=2,
        load_best_model_at_end=True,
        metric_for_best_model="eval_loss",
        greater_is_better=False,
        dataloader_pin_memory=False,
        remove_unused_columns=False,
        report_to=None,
    )
    
    data_collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=False,
    )
    
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset["train"],
        eval_dataset=train_dataset["test"],
        data_collator=data_collator,
    )
    
    print("🏋️ Starting Qwen3 training...")
    trainer.train()
    
    print("💾 Saving final model...")
    trainer.save_model("./qwen3-python-final")
    tokenizer.save_pretrained("./qwen3-python-final")
    
    model_info = {
        "base_model": "Qwen/Qwen3-8B",
        "dataset": "iamtarun/python_code_instructions_18k_alpaca",
        "lora_config": {
            "r": 16,
            "alpha": 32,
            "target_modules": ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]
        },
        "agentic_capabilities": "Preserved with thinking mode disabled during training"
    }
    
    with open("./qwen3-python-final/model_info.json", "w") as f:
        json.dump(model_info, f, indent=2)
    
    print("✅ Qwen3 training completed!")
    print("📁 Model saved to: ./qwen3-python-final/")
    print("🤖 Ready for Ollama conversion with agentic capabilities!")

if __name__ == "__main__":
    main()
