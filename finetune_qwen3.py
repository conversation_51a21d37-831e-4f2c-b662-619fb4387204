#!/usr/bin/env python3
"""
Fine-tune Qwen3-8B on Python code instructions dataset
Optimized for Ollama deployment with proper model saving
"""

import os
import torch
from datasets import load_dataset
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling
)
from peft import LoraConfig, get_peft_model, TaskType
import json

def setup_environment():
    """Setup the training environment"""
    print("🚀 Setting up fine-tuning environment...")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name(0)}")
        print(f"VRAM: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")

def load_model_and_tokenizer():
    """Load Qwen3-8B model and tokenizer"""
    print("📦 Loading Qwen3-8B model and tokenizer...")
    
    model_name = "Qwen/Qwen2.5-7B-Instruct"  # Using available model
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(
        model_name,
        trust_remote_code=True,
        padding_side="right"
    )
    
    # Add pad token if it doesn't exist
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Load model with optimizations for fine-tuning
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        torch_dtype=torch.bfloat16,
        device_map="auto",
        trust_remote_code=True,
        attn_implementation="flash_attention_2" if torch.cuda.is_available() else None
    )
    
    return model, tokenizer

def setup_lora_config():
    """Setup LoRA configuration for efficient fine-tuning"""
    print("⚙️ Setting up LoRA configuration...")
    
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        r=16,  # Rank
        lora_alpha=32,  # Alpha parameter
        lora_dropout=0.1,  # Dropout
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
        bias="none",
    )
    
    return lora_config

def prepare_dataset(tokenizer):
    """Load and prepare the Python code instructions dataset"""
    print("📚 Loading dataset: iamtarun/python_code_instructions_18k_alpaca...")
    
    # Load dataset
    dataset = load_dataset("iamtarun/python_code_instructions_18k_alpaca")
    
    def format_instruction(example):
        """Format the instruction-response pairs"""
        instruction = example.get('instruction', '')
        input_text = example.get('input', '')
        output = example.get('output', '')
        
        # Create a proper prompt format
        if input_text:
            prompt = f"### Instruction:\n{instruction}\n\n### Input:\n{input_text}\n\n### Response:\n{output}"
        else:
            prompt = f"### Instruction:\n{instruction}\n\n### Response:\n{output}"
        
        return {"text": prompt}
    
    # Format the dataset
    formatted_dataset = dataset.map(format_instruction)
    
    def tokenize_function(examples):
        """Tokenize the text"""
        tokenized = tokenizer(
            examples["text"],
            truncation=True,
            padding=False,
            max_length=2048,
            return_tensors=None
        )
        tokenized["labels"] = tokenized["input_ids"].copy()
        return tokenized
    
    # Tokenize dataset
    tokenized_dataset = formatted_dataset.map(
        tokenize_function,
        batched=True,
        remove_columns=formatted_dataset["train"].column_names
    )
    
    return tokenized_dataset

def main():
    """Main training function"""
    setup_environment()
    
    # Load model and tokenizer
    model, tokenizer = load_model_and_tokenizer()
    
    # Setup LoRA
    lora_config = setup_lora_config()
    model = get_peft_model(model, lora_config)
    
    print(f"📊 Trainable parameters: {model.num_parameters():,}")
    print(f"📊 Total parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Prepare dataset
    dataset = prepare_dataset(tokenizer)
    
    # Split dataset (use 90% for training, 10% for validation)
    train_dataset = dataset["train"].train_test_split(test_size=0.1, seed=42)
    
    print(f"📊 Training samples: {len(train_dataset['train']):,}")
    print(f"📊 Validation samples: {len(train_dataset['test']):,}")
    
    # Training arguments
    training_args = TrainingArguments(
        output_dir="./qwen3-python-finetune",
        num_train_epochs=3,
        per_device_train_batch_size=2,
        per_device_eval_batch_size=2,
        gradient_accumulation_steps=8,
        warmup_steps=100,
        learning_rate=2e-4,
        fp16=False,
        bf16=True,
        logging_steps=10,
        evaluation_strategy="steps",
        eval_steps=100,
        save_steps=500,
        save_total_limit=3,
        load_best_model_at_end=True,
        metric_for_best_model="eval_loss",
        greater_is_better=False,
        dataloader_pin_memory=False,
        remove_unused_columns=False,
        report_to=None,  # Disable wandb
    )
    
    # Data collator
    data_collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=False,
    )
    
    # Initialize trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset["train"],
        eval_dataset=train_dataset["test"],
        data_collator=data_collator,
    )
    
    # Start training
    print("🏋️ Starting training...")
    trainer.train()
    
    # Save the final model
    print("💾 Saving final model...")
    trainer.save_model("./qwen3-python-final")
    tokenizer.save_pretrained("./qwen3-python-final")
    
    # Save model info for Ollama conversion
    model_info = {
        "base_model": "Qwen/Qwen2.5-7B-Instruct",
        "dataset": "iamtarun/python_code_instructions_18k_alpaca",
        "lora_config": {
            "r": 16,
            "alpha": 32,
            "target_modules": ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]
        },
        "training_params": {
            "epochs": 3,
            "batch_size": 2,
            "learning_rate": 2e-4
        }
    }
    
    with open("./qwen3-python-final/model_info.json", "w") as f:
        json.dump(model_info, f, indent=2)
    
    print("✅ Training completed!")
    print("📁 Model saved to: ./qwen3-python-final/")
    print("🦙 Ready for Ollama conversion!")

if __name__ == "__main__":
    main()
