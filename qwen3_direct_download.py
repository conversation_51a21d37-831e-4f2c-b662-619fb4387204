#!/usr/bin/env python3
"""
Qwen3-8B Fine-tuning Script with Direct Dataset Download
Bypasses datasets library completely to avoid caching issues
"""

import os
import json
import requests
import torch
import shutil
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM, 
    TrainingArguments, 
    Trainer,
    DataCollatorForLanguageModeling
)
from peft import LoraConfig, get_peft_model, TaskType
from torch.utils.data import Dataset
import gc

class DirectDataset(Dataset):
    """Custom dataset that loads data directly without caching"""
    
    def __init__(self, data, tokenizer, max_length=2048):
        self.data = data
        self.tokenizer = tokenizer
        self.max_length = max_length
        
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        
        # Create simple prompt format
        instruction = item.get('instruction', '')
        input_text = item.get('input', '')
        output = item.get('output', '')
        
        if input_text:
            prompt = f"Instruction: {instruction}\nInput: {input_text}\nOutput: {output}"
        else:
            prompt = f"Instruction: {instruction}\nOutput: {output}"
        
        # Tokenize
        encoding = self.tokenizer(
            prompt,
            truncation=True,
            max_length=self.max_length,
            padding="max_length",
            return_tensors="pt"
        )
        
        return {
            "input_ids": encoding["input_ids"].flatten(),
            "attention_mask": encoding["attention_mask"].flatten(),
            "labels": encoding["input_ids"].flatten()
        }

def download_dataset_direct():
    """Download dataset directly from Hugging Face without using datasets library"""
    print("🌐 Downloading dataset directly from Hugging Face...")
    
    # Direct download URL for the dataset
    url = "https://huggingface.co/datasets/iamtarun/python_code_instructions_18k_alpaca/resolve/main/python_code_instructions_18k_alpaca.json"
    
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        print("📥 Parsing JSON data...")
        data = response.json()
        
        print(f"✅ Successfully loaded {len(data)} examples")
        return data
        
    except Exception as e:
        print(f"❌ Error downloading dataset: {e}")
        print("🔄 Trying alternative download method...")
        
        # Alternative: try the raw file URL
        alt_url = "https://huggingface.co/datasets/iamtarun/python_code_instructions_18k_alpaca/raw/main/python_code_instructions_18k_alpaca.json"
        try:
            response = requests.get(alt_url, stream=True)
            response.raise_for_status()
            data = response.json()
            print(f"✅ Successfully loaded {len(data)} examples via alternative URL")
            return data
        except Exception as e2:
            print(f"❌ Alternative download also failed: {e2}")
            raise

def cleanup_memory():
    """Clean up GPU memory"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    gc.collect()

def main():
    print("🚀 Setting up Qwen3 fine-tuning environment...")
    
    # System info
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name()}")
        print(f"VRAM: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    # Clear any existing cache
    print("🧹 Clearing all caches...")
    cache_dirs = [
        os.path.expanduser("~/.cache/huggingface"),
        "/tmp/datasets",
        "/root/.cache/huggingface",
        "/tmp/huggingface"
    ]
    
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir):
            try:
                shutil.rmtree(cache_dir)
                print(f"   Cleared: {cache_dir}")
            except:
                pass
    
    cleanup_memory()
    
    # Load model and tokenizer
    print("📦 Loading Qwen3-8B model and tokenizer...")
    model_name = "Qwen/Qwen3-8B"
    
    tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        torch_dtype=torch.float16,
        device_map="auto",
        trust_remote_code=True
    )
    
    # Setup LoRA
    print("⚙️ Setting up LoRA configuration for Qwen3...")
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        r=16,
        lora_alpha=32,
        lora_dropout=0.1,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]
    )
    
    model = get_peft_model(model, lora_config)
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"📊 Trainable parameters: {trainable_params:,}")
    
    # Download and prepare dataset
    print("📚 Loading dataset with direct download...")
    raw_data = download_dataset_direct()
    
    # Take a subset for faster training (you can increase this)
    subset_size = min(1000, len(raw_data))  # Use 1000 examples for quick test
    raw_data = raw_data[:subset_size]
    print(f"📝 Using {len(raw_data)} examples for training")
    
    # Create dataset
    dataset = DirectDataset(raw_data, tokenizer)
    
    # Split into train/eval
    train_size = int(0.9 * len(dataset))
    eval_size = len(dataset) - train_size
    train_dataset, eval_dataset = torch.utils.data.random_split(dataset, [train_size, eval_size])
    
    print(f"📊 Train samples: {len(train_dataset)}")
    print(f"📊 Eval samples: {len(eval_dataset)}")
    
    # Training arguments
    training_args = TrainingArguments(
        output_dir="./qwen3-finetuned",
        num_train_epochs=2,
        per_device_train_batch_size=1,
        per_device_eval_batch_size=1,
        gradient_accumulation_steps=4,
        warmup_steps=100,
        learning_rate=2e-4,
        fp16=True,
        logging_steps=10,
        evaluation_strategy="steps",
        eval_steps=50,
        save_steps=100,
        save_total_limit=2,
        remove_unused_columns=False,
        dataloader_pin_memory=False,
    )
    
    # Data collator
    data_collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=False,
    )
    
    # Create trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=eval_dataset,
        data_collator=data_collator,
    )
    
    # Start training
    print("🎯 Starting training...")
    trainer.train()
    
    # Save the model
    print("💾 Saving fine-tuned model...")
    trainer.save_model("./qwen3-finetuned-final")
    tokenizer.save_pretrained("./qwen3-finetuned-final")
    
    print("✅ Training completed successfully!")
    print("📁 Model saved to: ./qwen3-finetuned-final")
    
    # Final cleanup
    cleanup_memory()

if __name__ == "__main__":
    main()
