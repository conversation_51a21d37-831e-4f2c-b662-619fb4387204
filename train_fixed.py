#!/usr/bin/env python3
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, TrainingArguments, Trainer, DataCollatorForLanguageModeling
from peft import LoraConfig, get_peft_model, TaskType
import requests
import json
from torch.utils.data import Dataset

class SimpleDataset(Dataset):
    def __init__(self, data, tokenizer, max_length=512):
        self.data = data
        self.tokenizer = tokenizer
        self.max_length = max_length
        
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        text = f"Instruction: {item['instruction']}\nOutput: {item['output']}"
        
        encoding = self.tokenizer(
            text,
            truncation=True,
            max_length=self.max_length,
            padding="max_length",
            return_tensors="pt"
        )
        
        return {
            "input_ids": encoding["input_ids"].flatten(),
            "attention_mask": encoding["attention_mask"].flatten(),
            "labels": encoding["input_ids"].flatten()
        }

print("🚀 Loading local Qwen3-8B model...")

# Load from local directory
model_path = "./qwen3-8b-model"
tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True, local_files_only=True)
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

model = AutoModelForCausalLM.from_pretrained(
    model_path,
    torch_dtype=torch.float16,
    device_map="auto",
    trust_remote_code=True,
    local_files_only=True
)

print("✅ Model loaded successfully!")

# Setup LoRA
lora_config = LoraConfig(
    task_type=TaskType.CAUSAL_LM,
    r=16,
    lora_alpha=32,
    lora_dropout=0.1,
    target_modules=["q_proj", "k_proj", "v_proj", "o_proj"]
)

model = get_peft_model(model, lora_config)
print(f"📊 Trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

# Try multiple dataset URLs
print("📚 Downloading dataset...")
urls = [
    "https://huggingface.co/datasets/iamtarun/python_code_instructions_18k_alpaca/raw/main/python_code_instructions_18k_alpaca.json",
    "https://huggingface.co/datasets/iamtarun/python_code_instructions_18k_alpaca/resolve/main/python_code_instructions_18k_alpaca.json"
]

data = None
for url in urls:
    try:
        print(f"Trying: {url}")
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        data = response.json()
        print(f"✅ Successfully loaded {len(data)} examples")
        break
    except Exception as e:
        print(f"❌ Failed: {e}")
        continue

if data is None:
    print("❌ All dataset URLs failed. Creating dummy data for testing...")
    data = [
        {"instruction": "Write a Python function to add two numbers", "output": "def add(a, b):\n    return a + b"},
        {"instruction": "Create a list comprehension", "output": "[x**2 for x in range(10)]"},
        {"instruction": "Write a for loop", "output": "for i in range(5):\n    print(i)"}
    ]

# Use small subset
data = data[:50]  # Just 50 examples
print(f"📝 Using {len(data)} examples for training")

# Create dataset
dataset = SimpleDataset(data, tokenizer)

# Training arguments
training_args = TrainingArguments(
    output_dir="./qwen3-finetuned",
    num_train_epochs=1,
    per_device_train_batch_size=1,
    gradient_accumulation_steps=2,
    learning_rate=2e-4,
    fp16=True,
    logging_steps=5,
    save_steps=25,
    remove_unused_columns=False,
)

# Data collator
data_collator = DataCollatorForLanguageModeling(
    tokenizer=tokenizer,
    mlm=False,
)

# Create trainer
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=dataset,
    data_collator=data_collator,
)

print("🎯 Starting training...")
trainer.train()

print("✅ Training completed!")
trainer.save_model("./qwen3-finetuned-final")
print("💾 Model saved!")
