#!/bin/bash

echo "🚀 Setting up fine-tuning environment on RunPod..."

# Update system
apt-get update -y

# Install required packages
echo "📦 Installing required packages..."
pip install --upgrade pip

# Install core ML libraries
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# Install transformers and related libraries
pip install transformers==4.36.0
pip install datasets==2.14.0
pip install peft==0.7.0
pip install accelerate==0.24.0
pip install bitsandbytes==0.41.0

# Install additional utilities
pip install wandb
pip install tensorboard
pip install scipy
pip install scikit-learn

# Install flash attention for faster training (if available)
pip install flash-attn --no-build-isolation || echo "⚠️ Flash attention not available, continuing without it"

echo "✅ Environment setup complete!"
echo "🔍 Checking GPU status..."
nvidia-smi

echo "🐍 Python packages installed:"
pip list | grep -E "(torch|transformers|datasets|peft|accelerate)"

echo "🎯 Ready to start fine-tuning!"
