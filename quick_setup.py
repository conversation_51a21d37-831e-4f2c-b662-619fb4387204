#!/usr/bin/env python3
"""
Quick setup script - paste this into the RunPod terminal
"""

# First, let's install the required packages
import subprocess
import sys

print("🚀 Installing packages...")
packages = [
    "transformers==4.36.0",
    "datasets==2.14.0", 
    "peft==0.7.0",
    "accelerate==0.24.0",
    "bitsandbytes==0.41.0"
]

for pkg in packages:
    subprocess.run([sys.executable, "-m", "pip", "install", pkg], check=True)

print("✅ Setup complete! Now creating training script...")

# Create the training script
training_script = '''
import torch
from datasets import load_dataset
from transformers import AutoTokenizer, AutoModelForCausalLM, TrainingArguments, Trainer, DataCollatorForLanguageModeling
from peft import LoraConfig, get_peft_model, TaskType
import json

print("🚀 Starting Qwen fine-tuning...")
print(f"CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"GPU: {torch.cuda.get_device_name(0)}")

# Load model and tokenizer
print("📦 Loading model...")
model_name = "Qwen/Qwen2.5-7B-Instruct"
tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True, padding_side="right")
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

model = AutoModelForCausalLM.from_pretrained(
    model_name,
    torch_dtype=torch.bfloat16,
    device_map="auto",
    trust_remote_code=True
)

# Setup LoRA
print("⚙️ Setting up LoRA...")
lora_config = LoraConfig(
    task_type=TaskType.CAUSAL_LM,
    r=16,
    lora_alpha=32,
    lora_dropout=0.1,
    target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
    bias="none",
)
model = get_peft_model(model, lora_config)
print(f"📊 Trainable parameters: {model.num_parameters():,}")

# Load dataset
print("📚 Loading dataset...")
dataset = load_dataset("iamtarun/python_code_instructions_18k_alpaca")

def format_instruction(example):
    instruction = example.get('instruction', '')
    input_text = example.get('input', '')
    output = example.get('output', '')
    
    if input_text:
        prompt = f"### Instruction:\\n{instruction}\\n\\n### Input:\\n{input_text}\\n\\n### Response:\\n{output}"
    else:
        prompt = f"### Instruction:\\n{instruction}\\n\\n### Response:\\n{output}"
    
    return {"text": prompt}

formatted_dataset = dataset.map(format_instruction)

def tokenize_function(examples):
    tokenized = tokenizer(examples["text"], truncation=True, padding=False, max_length=2048, return_tensors=None)
    tokenized["labels"] = tokenized["input_ids"].copy()
    return tokenized

tokenized_dataset = formatted_dataset.map(tokenize_function, batched=True, remove_columns=formatted_dataset["train"].column_names)
train_dataset = tokenized_dataset["train"].train_test_split(test_size=0.1, seed=42)

print(f"📊 Training samples: {len(train_dataset['train']):,}")

# Training arguments
training_args = TrainingArguments(
    output_dir="./qwen3-python-finetune",
    num_train_epochs=2,
    per_device_train_batch_size=1,
    gradient_accumulation_steps=16,
    warmup_steps=50,
    learning_rate=2e-4,
    bf16=True,
    logging_steps=10,
    evaluation_strategy="steps",
    eval_steps=100,
    save_steps=500,
    save_total_limit=2,
    remove_unused_columns=False,
    report_to=None,
)

data_collator = DataCollatorForLanguageModeling(tokenizer=tokenizer, mlm=False)

trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=train_dataset["train"],
    eval_dataset=train_dataset["test"],
    data_collator=data_collator,
)

print("🏋️ Starting training...")
trainer.train()

print("💾 Saving model...")
trainer.save_model("./qwen3-python-final")
tokenizer.save_pretrained("./qwen3-python-final")

print("✅ Training completed! Model saved to ./qwen3-python-final/")
'''

with open("train_qwen.py", "w") as f:
    f.write(training_script)

print("📝 Training script created!")
print("🎯 Run: python train_qwen.py")
