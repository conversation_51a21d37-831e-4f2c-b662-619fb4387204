#!/bin/bash

echo "🚀 Direct Qwen3-8B Model Download Script"
echo "========================================"

# Create model directory
MODEL_DIR="./qwen3-8b-model"
mkdir -p "$MODEL_DIR"
cd "$MODEL_DIR"

echo "📁 Created directory: $MODEL_DIR"
echo "💾 Current disk space:"
df -h .

# Base URL for Qwen3-8B model
BASE_URL="https://huggingface.co/Qwen/Qwen3-8B/resolve/main"

# List of files to download (essential files only)
FILES=(
    "config.json"
    "tokenizer.json"
    "tokenizer_config.json"
    "vocab.json"
    "merges.txt"
    "model.safetensors.index.json"
    "model-00001-of-00005.safetensors"
    "model-00002-of-00005.safetensors"
    "model-00003-of-00005.safetensors"
    "model-00004-of-00005.safetensors"
    "model-00005-of-00005.safetensors"
)

# Function to download with resume capability
download_file() {
    local file=$1
    local url="$BASE_URL/$file"
    
    echo ""
    echo "📥 Downloading: $file"
    echo "🔗 URL: $url"
    
    # Use wget with resume capability and progress bar
    wget -c --progress=bar:force:noscroll --timeout=30 --tries=3 "$url" -O "$file"
    
    if [ $? -eq 0 ]; then
        echo "✅ Successfully downloaded: $file"
        ls -lh "$file"
    else
        echo "❌ Failed to download: $file"
        return 1
    fi
}

# Download small files first (config, tokenizer)
echo ""
echo "🔧 Downloading configuration and tokenizer files..."
for file in "config.json" "tokenizer.json" "tokenizer_config.json" "vocab.json" "merges.txt" "model.safetensors.index.json"; do
    download_file "$file"
    if [ $? -ne 0 ]; then
        echo "❌ Failed to download essential file: $file"
        exit 1
    fi
done

echo ""
echo "✅ All configuration files downloaded successfully!"
echo "💾 Current disk usage:"
du -sh .
df -h .

echo ""
echo "🔄 Now downloading model weight files (this will take a while)..."
echo "💡 You can monitor progress and interrupt/resume if needed"

# Download model files one by one
for file in "model-00001-of-00005.safetensors" "model-00002-of-00005.safetensors" "model-00003-of-00005.safetensors" "model-00004-of-00005.safetensors" "model-00005-of-00005.safetensors"; do
    echo ""
    echo "📦 Starting download: $file"
    download_file "$file"
    
    if [ $? -eq 0 ]; then
        echo "✅ Completed: $file"
        echo "💾 Current total size:"
        du -sh .
        df -h .
    else
        echo "❌ Download failed for: $file"
        echo "🔄 You can resume later by running this script again"
        echo "📝 Already downloaded files will be skipped"
        exit 1
    fi
done

echo ""
echo "🎉 All files downloaded successfully!"
echo "📊 Final model directory contents:"
ls -lah
echo ""
echo "💾 Total size:"
du -sh .

echo ""
echo "✅ Qwen3-8B model ready for training!"
echo "📁 Model location: $(pwd)"
