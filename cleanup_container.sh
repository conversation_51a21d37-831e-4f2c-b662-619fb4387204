#!/bin/bash

echo "🧹 RunPod Container Cleanup Script"
echo "=================================="

# Function to show disk usage
show_disk_usage() {
    echo "💾 Current disk usage:"
    df -h /
    echo ""
}

# Show initial disk usage
echo "📊 BEFORE cleanup:"
show_disk_usage

# 1. Clear all Hugging Face caches
echo "🗑️  Clearing Hugging Face caches..."
rm -rf ~/.cache/huggingface/
rm -rf /root/.cache/huggingface/
rm -rf /tmp/huggingface/
rm -rf ~/.cache/torch/
rm -rf /root/.cache/torch/
echo "   ✅ Hugging Face caches cleared"

# 2. Clear datasets caches specifically
echo "🗑️  Clearing datasets caches..."
rm -rf ~/.cache/huggingface/datasets/
rm -rf /root/.cache/huggingface/datasets/
rm -rf /tmp/datasets/
rm -rf ~/.cache/datasets/
rm -rf /root/.cache/datasets/
echo "   ✅ Datasets caches cleared"

# 3. Clear pip cache
echo "🗑️  Clearing pip cache..."
pip cache purge 2>/dev/null || echo "   No pip cache to clear"
echo "   ✅ Pip cache cleared"

# 4. Clear temporary files
echo "🗑️  Clearing temporary files..."
rm -rf /tmp/*
rm -rf /var/tmp/*
echo "   ✅ Temporary files cleared"

# 5. Clear Python cache files
echo "🗑️  Clearing Python cache files..."
find / -name "*.pyc" -delete 2>/dev/null || true
find / -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
echo "   ✅ Python cache files cleared"

# 6. Clear old training outputs (if any)
echo "🗑️  Clearing old training outputs..."
rm -rf ./qwen3-finetuned*
rm -rf ./checkpoint-*
rm -rf ./runs/
rm -rf ./wandb/
echo "   ✅ Old training outputs cleared"

# 7. Clear Docker/container logs (if accessible)
echo "🗑️  Clearing container logs..."
truncate -s 0 /var/log/*.log 2>/dev/null || true
echo "   ✅ Container logs cleared"

# 8. Clear any downloaded model files that aren't needed
echo "🗑️  Clearing unnecessary model files..."
rm -rf ~/.cache/transformers/
rm -rf /root/.cache/transformers/
echo "   ✅ Unnecessary model files cleared"

# 9. Force garbage collection
echo "🗑️  Running Python garbage collection..."
python3 -c "
import gc
import torch
if torch.cuda.is_available():
    torch.cuda.empty_cache()
    print('   GPU cache cleared')
gc.collect()
print('   Python garbage collection completed')
"

# 10. Clear any remaining cache directories
echo "🗑️  Final cache cleanup..."
rm -rf ~/.cache/*
rm -rf /root/.cache/*
echo "   ✅ Final cache cleanup completed"

echo ""
echo "📊 AFTER cleanup:"
show_disk_usage

echo "✅ Container cleanup completed!"
echo ""
echo "🚀 Ready for fresh training run!"
echo "   Run: python qwen3_direct_download.py"
